using UnityEngine;

public class GameConfig
{
    public static string VERSION = "1.1.17";
    public static int BUILD_VER = 1;
    public static void SetVer(string value)
    {
        VERSION = value;
    }
    public static string GetVer()
    {
        return VERSION;
    }
    public static void SetBuildVer(int value)
    {
        BUILD_VER = value;
    }
    public static int GetBuildVer()
    {
        return BUILD_VER;
    }


    public static bool UseOnlineProcess = true;
    private static string _cdnBase = "https://xuan-cdn.feigo.fun/ggi/cpghl/";

    public static string CdnBase
    {
        get
        {
            return "http://192.168.77.66/majiang2/";
            return _cdnBase;
        }
    }
    private static string _cdnUrl;
    public static string CdnUrl
    {
        get
        {
            if (string.IsNullOrEmpty(_cdnUrl))
            {
#if UNITY_STANDALONE_WIN
                _cdnUrl = CdnBase + "StandaloneWindows64";
#elif UNITY_ANDROID
                _cdnUrl = CdnBase + "Android";
#elif UNITY_STANDALONE_OSX
                _cdnUrl = CdnBase + "StandaloneOSX";
#elif UNITY_WEBGL
                _cdnUrl = CdnBase + "WebGL";
#endif
                _cdnUrl += $"/{GetVer()}";
            }
            return _cdnUrl;
        }
    }
}